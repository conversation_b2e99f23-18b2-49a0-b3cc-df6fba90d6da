import SwiftUI
import UIKit

// 背景选择视图
struct BackgroundSelectionView: View {
    @Environment(\.presentationMode) var presentationMode
    var foregroundImage: UIImage
    var originalImage: UIImage
    var selectionFrame: CGRect
    @State private var backgroundImage: UIImage?
    @State private var showImagePicker = false
    @State private var showTemplateView = false
    // 添加图片显示信息状态变量
    @State private var backgroundDisplayInfo: DisplayInfo = .zero
    @State private var foregroundDisplayInfo: DisplayInfo = .zero
    var onBack: () -> Void
    
    // 添加通知观察器
    @State private var templateUpdateObserver: NSObjectProtocol? = nil
    
    // 创建图片显示信息结构体
    struct DisplayInfo {
        var scale: CGFloat
        var size: CGSize
        var displaySize: CGSize
        var position: CGPoint
        
        static var zero: DisplayInfo {
            return DisplayInfo(scale: 1.0, size: .zero, displaySize: .zero, position: .zero)
        }
    }
    
    // 添加初始化方法，提供默认的onBack回调
    init(foregroundImage: UIImage, originalImage: UIImage = UIImage(), selectionFrame: CGRect = .zero, onBack: @escaping () -> Void = {}) {
        self.foregroundImage = foregroundImage
        self.originalImage = originalImage
        self.selectionFrame = selectionFrame
        self.onBack = onBack
    }
    
    var body: some View {
        ZStack {
            // 背景颜色
            Color.white.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 返回按钮
                    Button(action: {
                        onBack()
                    }) {
                        Image(systemName: "arrow.left")
                            .font(.system(size: 18))
                            .foregroundColor(.black)
                    }
                    .padding(.leading, 16)
                    
                    Spacer()
                    
                    // 标题
                    Text("背景选择")
                        .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    // 占位，保持标题居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 20)
                
                // 图片展示区域
                GeometryReader { geometry in
                    ZStack {
                        // 背景图片
                        if let backgroundImg = backgroundImage {
                            Image(uiImage: backgroundImg)
                                .resizable()
                                .scaledToFit()
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .overlay(
                                    GeometryReader { imageGeo in
                                        Color.clear
                                            .onAppear {
                                                // 计算背景图展示信息
                                                let displayScale = min(
                                                    geometry.size.width / backgroundImg.size.width,
                                                    geometry.size.height / backgroundImg.size.height
                                                )
                                                
                                                let displayWidth = backgroundImg.size.width * displayScale
                                                let displayHeight = backgroundImg.size.height * displayScale
                                                
                                                backgroundDisplayInfo = DisplayInfo(
                                                    scale: displayScale,
                                                    size: CGSize(width: backgroundImg.size.width, height: backgroundImg.size.height),
                                                    displaySize: CGSize(width: displayWidth, height: displayHeight),
                                                    position: CGPoint(
                                                        x: (geometry.size.width - displayWidth) / 2,
                                                        y: (geometry.size.height - displayHeight) / 2
                                                    )
                                                )
                                                
                                                // 如果前景图信息未初始化，则初始化
                                                if foregroundDisplayInfo.size == .zero {
                                                    initializeForegroundDisplay(geometry: geometry)
                                                }
                                            }
                                    }
                                )
                        }
                        
                        // 前景图片（人物）- 按实际像素对齐显示
                        // 只有在没有背景图时才显示前景图
                        if backgroundImage == nil {
                            if !foregroundDisplayInfo.displaySize.width.isZero {
                                Image(uiImage: foregroundImage)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(
                                        width: foregroundDisplayInfo.displaySize.width,
                                        height: foregroundDisplayInfo.displaySize.height
                                    )
                                    .position(
                                        x: backgroundDisplayInfo.position.x + foregroundDisplayInfo.displaySize.width / 2,
                                        y: backgroundDisplayInfo.position.y + foregroundDisplayInfo.displaySize.height / 2
                                    )
                            } else {
                                // 兼容原来的显示方式，确保图片在初始化过程中也能显示
                                Image(uiImage: foregroundImage)
                                    .resizable()
                                    .scaledToFit()
                                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                            }
                        }
                    }
                }
                .padding(.bottom, 20)
                
                // 底部按钮
                HStack(spacing: 20) {
                    // 本地上传按钮
                    Button(action: {
                        showImagePicker = true
                    }) {
                        HStack(spacing: 5) {
                            Image(systemName: "photo")
                                .font(.system(size: 14))
                            Text("本地上传")
                                .font(Font.custom("PingFang SC", size: 15))
                        }
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        .frame(width: 91, height: 33)
                        .background(
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 91, height: 33)
                                .background(.white)
                                .cornerRadius(30)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 30)
                                        .inset(by: 0.25)
                                        .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                                )
                        )
                    }
                    
                    // 在线相册按钮
                    Button(action: {
                        showTemplateView = true
                    }) {
                        HStack(spacing: 5) {
                            Image(systemName: "photo.on.rectangle")
                                .font(.system(size: 14))
                            Text("在线相册")
                                .font(Font.custom("PingFang SC", size: 15))
                        }
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        .frame(width: 91, height: 33)
                        .background(
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 91, height: 33)
                                .background(.white)
                                .cornerRadius(30)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 30)
                                        .inset(by: 0.25)
                                        .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                                )
                        )
                    }
                    
                    // 下一步按钮
                    Button(action: {
                        navigateToImageAdjustment()
                    }) {
                        HStack(spacing: 5) {
                            Text("下一步")
                                .font(Font.custom("PingFang SC", size: 15))
                            Image(systemName: "arrow.right")
                                .font(.system(size: 14))
                        }
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        .frame(width: 71, height: 33)
                        .background(
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 71, height: 33)
                                .background(.white)
                                .cornerRadius(30)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 30)
                                        .inset(by: 0.25)
                                        .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                                )
                        )
                    }
                }
                .padding(.bottom, 30)
            }
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $showImagePicker) {
            ImagePickerView(sourceType: .photoLibrary) { image in
                if let image = image {
                    self.backgroundImage = image
                    // 重置显示信息，以便重新计算
                    self.backgroundDisplayInfo = .zero
                    self.foregroundDisplayInfo = .zero
                }
            }
        }
        .fullScreenCover(isPresented: $showTemplateView) {
            TemplateGalleryView(onSelectTemplate: { image in
                self.backgroundImage = image
                // 重置显示信息，以便重新计算
                self.backgroundDisplayInfo = .zero
                self.foregroundDisplayInfo = .zero
            })
        }
        .onAppear {
            // 注册通知观察器以接收高分辨率模板图片更新
            templateUpdateObserver = NotificationCenter.default.addObserver(
                forName: NSNotification.Name("UpdateTemplateImage"),
                object: nil,
                queue: .main
            ) { notification in
                if let highResImage = notification.userInfo?["image"] as? UIImage {
                    // 更新为高分辨率模板图片
                    self.backgroundImage = highResImage
                    // 重置显示信息，以便重新计算
                    self.backgroundDisplayInfo = .zero
                    self.foregroundDisplayInfo = .zero
                }
            }
        }
        .onDisappear {
            // 移除通知观察器
            if let observer = templateUpdateObserver {
                NotificationCenter.default.removeObserver(observer)
            }
        }
    }

    // 初始化前景图显示信息
    private func initializeForegroundDisplay(geometry: GeometryProxy) {
        // 使用与背景图相同的缩放比例
        let displayScale = backgroundDisplayInfo.scale

        // 计算前景图的显示尺寸
        let displayWidth = foregroundImage.size.width * displayScale
        let displayHeight = foregroundImage.size.height * displayScale

        foregroundDisplayInfo = DisplayInfo(
            scale: displayScale,
            size: CGSize(width: foregroundImage.size.width, height: foregroundImage.size.height),
            displaySize: CGSize(width: displayWidth, height: displayHeight),
            position: CGPoint(
                x: backgroundDisplayInfo.position.x,
                y: backgroundDisplayInfo.position.y
            )
        )
    }

    // 导航到图像调节页面
    private func navigateToImageAdjustment() {
        // 检查是否有背景图，如果没有则提示用户
        guard backgroundImage != nil else {
            // 显示提示框
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController {
                // 寻找当前显示的控制器
                var currentController = rootViewController
                while let presented = currentController.presentedViewController {
                    currentController = presented
                }

                // 显示警告
                let alert = UIAlertController(title: "提示", message: "请先选择背景图", preferredStyle: .alert)
                alert.addAction(UIAlertAction(title: "确定", style: .default))
                currentController.present(alert, animated: true)
            }
            return
        }

        // 有背景图的情况下继续导航
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {

            // 检查当前呈现的控制器，如果有，获取当前控制器
            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }

            // 创建图像调节页面
            let hostingController = UIHostingController(rootView:
                ImageAdjustmentView(
                    foregroundImage: self.foregroundImage,
                    backgroundImage: self.backgroundImage,
                    onBack: { [weak currentController] in
                        // 返回到背景选择页面
                        currentController?.dismiss(animated: true, completion: nil)
                    }
                )
            )
            hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen

            // 从当前控制器呈现图像调节页面
            currentController.present(hostingController, animated: true)
        }
    }
}
