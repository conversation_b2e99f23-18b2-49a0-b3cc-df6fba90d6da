import SwiftUI
import UIKit

struct CharacterSelectionView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState // 添加UserState环境对象
    @State private var selectedImage: UIImage
    @State private var originalImage: UIImage
    @State private var showImagePicker = false
    @State private var showCamera = false
    @State private var showImagePickerSheet = false
    
    // 蓝色矩形框数据
    @State private var frameRect: CGRect = .zero
    @State private var isDragging = false
    @State private var dragMode: DragMode = .none
    @State private var startLocation: CGPoint = .zero
    @State private var imageSize: CGSize = .zero
    
    // 用于计算图片与视图大小的关系
    @State private var imageFrame: CGRect = .zero
    
    // 添加一个状态变量，用于在图片更新后强制重新初始化裁剪框
    @State private var imageUpdateCounter: Int = 0
    
    // 拖动模式
    enum DragMode {
        case none, move, resizeLeft, resizeRight, resizeTop, resizeBottom, resizeTopLeft, resizeTopRight, resizeBottomLeft, resizeBottomRight
    }
    
    init(image: UIImage) {
        self._selectedImage = State(initialValue: image)
        self._originalImage = State(initialValue: image)
    }
    
    var body: some View {
        ZStack {
            // 背景颜色
            Color.white.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 返回按钮
                    Button(action: {
                        // 如果是从详情页模版创作
                        if userState.isCreatingFromTemplate {
                            // 清除缓存的背景图路径
                            userState.templateBackgroundPath = ""
                            // 重置创作来源标记
                            userState.isCreatingFromTemplate = false
                        }
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "arrow.left")
                            .font(.system(size: 18))
                            .foregroundColor(.black)
                    }
                    .padding(.leading, 16)
                    
                    Spacer()
                    
                    // 标题
                    Text("人物选择")
                        .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    // 占位，保持标题居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 20)
                
                // 图片展示和选择区域
                GeometryReader { geometry in
                    ZStack {
                        // 图片
                        Image(uiImage: selectedImage)
                            .resizable()
                            .scaledToFit()
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .overlay(
                                GeometryReader { imageGeo in
                                    Color.clear
                                        .onAppear {
                                            // 记录图片实际大小
                                            self.imageFrame = imageGeo.frame(in: .global)
                                            
                                            // 初始化蓝色矩形框
                                            if self.frameRect == .zero {
                                                initializeSelectionFrame(imageGeo: imageGeo)
                                            }
                                        }
                                        // 添加id修饰符，当imageUpdateCounter变化时强制重新计算
                                        .id("imageOverlay_\(imageUpdateCounter)")
                                }
                            )
                        
                        // 蓝色矩形框
                        SelectionRectangleView(
                            frameRect: $frameRect,
                            isDragging: $isDragging
                        )
                        .frame(width: geometry.size.width, height: geometry.size.height)
                    }
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                isDragging = true
                                
                                if startLocation == .zero {
                                    startLocation = value.location
                                    
                                    // 确定拖动模式
                                    let touchPoint = value.location
                                    let edgeThreshold: CGFloat = 30
                                    
                                    // 检查是否在边缘或角落
                                    let nearLeft = abs(touchPoint.x - frameRect.minX) < edgeThreshold
                                    let nearRight = abs(touchPoint.x - frameRect.maxX) < edgeThreshold
                                    let nearTop = abs(touchPoint.y - frameRect.minY) < edgeThreshold
                                    let nearBottom = abs(touchPoint.y - frameRect.maxY) < edgeThreshold
                                    
                                    // 检查四个角落
                                    if nearLeft && nearTop {
                                        dragMode = .resizeTopLeft
                                    } else if nearRight && nearTop {
                                        dragMode = .resizeTopRight
                                    } else if nearLeft && nearBottom {
                                        dragMode = .resizeBottomLeft
                                    } else if nearRight && nearBottom {
                                        dragMode = .resizeBottomRight
                                    } 
                                    // 检查四条边
                                    else if nearLeft {
                                        dragMode = .resizeLeft
                                    } else if nearRight {
                                        dragMode = .resizeRight
                                    } else if nearTop {
                                        dragMode = .resizeTop
                                    } else if nearBottom {
                                        dragMode = .resizeBottom
                                    } else if frameRect.contains(touchPoint) {
                                        dragMode = .move
                                    } else {
                                        dragMode = .none
                                    }
                                    
                                    return
                                }
                                
                                let xOffset = value.location.x - startLocation.x
                                let yOffset = value.location.y - startLocation.y
                                
                                // 最小尺寸限制
                                let minSize: CGFloat = 100
                                
                                switch dragMode {
                                case .move:
                                    // 移动整个框
                                    var newRect = frameRect
                                    newRect.origin.x += xOffset
                                    newRect.origin.y += yOffset
                                    
                                    // 确保框不超出图片边界
                                    newRect.origin.x = max(0, min(newRect.origin.x, imageSize.width - newRect.width))
                                    newRect.origin.y = max(0, min(newRect.origin.y, imageSize.height - newRect.height))
                                    
                                    frameRect = newRect
                                    startLocation = value.location
                                    
                                case .resizeLeft:
                                    // 调整左边缘
                                    var newRect = frameRect
                                    newRect.origin.x += xOffset
                                    newRect.size.width -= xOffset
                                    
                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.x >= 0 && newRect.size.width >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }
                                    
                                case .resizeRight:
                                    // 调整右边缘
                                    var newRect = frameRect
                                    newRect.size.width += xOffset
                                    
                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.maxX <= imageSize.width && newRect.size.width >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }
                                    
                                case .resizeTop:
                                    // 调整上边缘
                                    var newRect = frameRect
                                    newRect.origin.y += yOffset
                                    newRect.size.height -= yOffset
                                    
                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.y >= 0 && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }
                                    
                                case .resizeBottom:
                                    // 调整下边缘
                                    var newRect = frameRect
                                    newRect.size.height += yOffset
                                    
                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.maxY <= imageSize.height && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }
                                    
                                case .resizeTopLeft:
                                    // 同时调整左边缘和上边缘
                                    var newRect = frameRect
                                    newRect.origin.x += xOffset
                                    newRect.origin.y += yOffset
                                    newRect.size.width -= xOffset
                                    newRect.size.height -= yOffset
                                    
                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.x >= 0 && newRect.origin.y >= 0 && 
                                       newRect.size.width >= minSize && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }
                                    
                                case .resizeTopRight:
                                    // 同时调整右边缘和上边缘
                                    var newRect = frameRect
                                    newRect.origin.y += yOffset
                                    newRect.size.width += xOffset
                                    newRect.size.height -= yOffset
                                    
                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.y >= 0 && newRect.maxX <= imageSize.width && 
                                       newRect.size.width >= minSize && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }
                                    
                                case .resizeBottomLeft:
                                    // 同时调整左边缘和下边缘
                                    var newRect = frameRect
                                    newRect.origin.x += xOffset
                                    newRect.size.width -= xOffset
                                    newRect.size.height += yOffset
                                    
                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.x >= 0 && newRect.maxY <= imageSize.height && 
                                       newRect.size.width >= minSize && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }
                                    
                                case .resizeBottomRight:
                                    // 同时调整右边缘和下边缘
                                    var newRect = frameRect
                                    newRect.size.width += xOffset
                                    newRect.size.height += yOffset
                                    
                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.maxX <= imageSize.width && newRect.maxY <= imageSize.height && 
                                       newRect.size.width >= minSize && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }
                                    
                                case .none:
                                    break
                                }
                            }
                            .onEnded { _ in
                                startLocation = .zero
                                isDragging = false
                                dragMode = .none
                            }
                    )
                }
                .padding(.bottom, 20)

                // 底部按钮区域
                VStack(spacing: 20) {
                    // 重新选择图片按钮
                    Button(action: {
                        showImagePickerSheet = true
                    }) {
                        HStack(spacing: 10) {
                            Image(systemName: "photo")
                                .font(.system(size: 16))
                            Text("重新选择图片")
                                .font(Font.custom("PingFang SC", size: 16))
                        }
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            RoundedRectangle(cornerRadius: 22)
                                .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 1)
                                .background(Color.white)
                        )
                    }
                    .padding(.horizontal, 20)

                    // 下一步按钮
                    Button(action: {
                        // 裁剪图片
                        let croppedImage = cropImage()

                        // 保存裁剪后的图片到UserState
                        userState.selectedCharacterImage = croppedImage

                        // 导航到背景选择页面
                        navigateToBackgroundSelection(croppedImage: croppedImage)
                    }) {
                        Text("下一步")
                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                            .background(
                                RoundedRectangle(cornerRadius: 22)
                                    .fill(Color(red: 0.98, green: 0.85, blue: 0.37))
                            )
                    }
                    .padding(.horizontal, 20)
                }
                .padding(.bottom, 30)
            }

            // 图片选择弹窗
            if showImagePickerSheet {
                ImagePickerSheet(
                    isPresented: $showImagePickerSheet,
                    onCameraSelected: {
                        showImagePickerSheet = false // 关闭底部弹出菜单
                        showCamera = true
                    },
                    onPhotoLibrarySelected: {
                        showImagePickerSheet = false // 关闭底部弹出菜单
                        showImagePicker = true
                    }
                )
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            // 初始化框
            frameRect = .zero
        }
        .fullScreenCover(isPresented: $showImagePicker) {
            ImagePickerView(sourceType: .photoLibrary) { image in
                if let image = image {
                    // 更新图片
                    selectedImage = image
                    originalImage = image

                    // 重置裁剪框并增加计数器，强制重新初始化
                    resetSelectionFrame()
                }
            }
        }
        .fullScreenCover(isPresented: $showCamera) {
            ImagePickerView(sourceType: .camera) { image in
                if let image = image {
                    // 更新图片
                    selectedImage = image
                    originalImage = image

                    // 重置裁剪框并增加计数器，强制重新初始化
                    resetSelectionFrame()
                }
            }
        }
    }

    // 初始化选择框
    private func initializeSelectionFrame(imageGeo: GeometryProxy) {
        let imageRect = imageGeo.frame(in: .local)
        imageSize = imageRect.size

        // 计算初始框的大小和位置（居中，占图片的60%）
        let frameWidth = imageSize.width * 0.6
        let frameHeight = imageSize.height * 0.6
        let frameX = (imageSize.width - frameWidth) / 2
        let frameY = (imageSize.height - frameHeight) / 2

        frameRect = CGRect(x: frameX, y: frameY, width: frameWidth, height: frameHeight)
    }

    // 重置选择框
    private func resetSelectionFrame() {
        frameRect = .zero
        imageUpdateCounter += 1
    }

    // 裁剪图片
    private func cropImage() -> UIImage {
        // 计算裁剪区域在原图中的比例
        let scaleX = originalImage.size.width / imageSize.width
        let scaleY = originalImage.size.height / imageSize.height

        let cropRect = CGRect(
            x: frameRect.origin.x * scaleX,
            y: frameRect.origin.y * scaleY,
            width: frameRect.size.width * scaleX,
            height: frameRect.size.height * scaleY
        )

        // 裁剪图片
        guard let cgImage = originalImage.cgImage?.cropping(to: cropRect) else {
            return originalImage
        }

        return UIImage(cgImage: cgImage)
    }

    // 导航到背景选择页面
    private func navigateToBackgroundSelection(croppedImage: UIImage?) {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {

            // 检查当前呈现的控制器，如果有，获取当前控制器
            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }

            // 检查导航逻辑
            if userState.navigateToImageAdjustFromTemplate && !userState.templateBackgroundPath.isEmpty {
                // 如果是从详情页模版创作，直接导航到图像调整页面
                // 创建一个临时的背景选择视图，但不实际显示
                let backgroundSelectionView = BackgroundSelectionView(
                    foregroundImage: croppedImage ?? selectedImage,
                    originalImage: originalImage,
                    selectionFrame: frameRect
                )

                // 从模板路径加载背景图像
                loadTemplateBackground(backgroundSelectionView: backgroundSelectionView)
            } else {
                // 正常流程 - 导航到背景选择页面
                let hostingController = UIHostingController(rootView:
                    BackgroundSelectionView(
                        foregroundImage: croppedImage ?? selectedImage,
                        originalImage: originalImage,
                        selectionFrame: frameRect,
                        onBack: { [weak currentController] in
                            // 返回到人物选择页面
                            currentController?.dismiss(animated: true, completion: nil)
                        }
                    )
                )
                hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen

                // 从当前控制器呈现背景选择页面
                currentController.present(hostingController, animated: true)
            }
        }
    }

    // 加载模版背景并导航到图像调整页面
    private func loadTemplateBackground(backgroundSelectionView: BackgroundSelectionView) {
        // 获取模板背景路径
        let backgroundPath = userState.templateBackgroundPath

        // 使用APIService加载背景图
        APIService.shared.getImageData(from: backgroundPath) { result in
            switch result {
            case .success(let image):
                DispatchQueue.main.async {
                    // 创建图像调整视图并导航
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {

                        // 检查当前呈现的控制器，如果有，获取当前控制器
                        var currentController = rootViewController
                        while let presented = currentController.presentedViewController {
                            currentController = presented
                        }

                        // 创建并导航到图像调整页面
                        let hostingController = UIHostingController(rootView:
                            ImageAdjustmentView(
                                foregroundImage: backgroundSelectionView.foregroundImage,
                                backgroundImage: image,
                                onBack: { [weak currentController] in
                                    // 返回到人物选择页面，保持模版创作模式
                                    currentController?.dismiss(animated: true, completion: nil)
                                }
                            )
                        )
                        hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen

                        // 从当前控制器呈现图像调整页面
                        currentController.present(hostingController, animated: true)
                    }
                }

            case .failure:
                // 加载失败，回退到正常流程
                DispatchQueue.main.async {
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {

                        // 检查当前呈现的控制器
                        var currentController = rootViewController
                        while let presented = currentController.presentedViewController {
                            currentController = presented
                        }

                        // 创建背景选择页面
                        let hostingController = UIHostingController(rootView:
                            BackgroundSelectionView(
                                foregroundImage: backgroundSelectionView.foregroundImage,
                                originalImage: backgroundSelectionView.originalImage,
                                selectionFrame: backgroundSelectionView.selectionFrame,
                                onBack: { [weak currentController] in
                                    currentController?.dismiss(animated: true, completion: nil)
                                }
                            )
                        )
                        hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen
                        currentController.present(hostingController, animated: true)

                        // 重置模板创作状态，失败时才重置
                        userState.navigateToImageAdjustFromTemplate = false
                    }
                }
            }
        }
    }
}

// 选择矩形框视图
struct SelectionRectangleView: View {
    @Binding var frameRect: CGRect
    @Binding var isDragging: Bool

    var body: some View {
        ZStack {
            // 蓝色矩形框
            Rectangle()
                .stroke(Color.blue, lineWidth: 2)
                .frame(width: frameRect.width, height: frameRect.height)
                .position(x: frameRect.midX, y: frameRect.midY)

            // 四个角的小方块
            ForEach(0..<4, id: \.self) { index in
                Rectangle()
                    .fill(Color.blue)
                    .frame(width: 12, height: 12)
                    .position(cornerPosition(for: index))
            }

            // 四条边的中点小方块
            ForEach(0..<4, id: \.self) { index in
                Rectangle()
                    .fill(Color.blue)
                    .frame(width: 8, height: 8)
                    .position(edgePosition(for: index))
            }
        }
        .opacity(isDragging ? 0.8 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isDragging)
    }

    // 计算角落位置
    private func cornerPosition(for index: Int) -> CGPoint {
        switch index {
        case 0: // 左上角
            return CGPoint(x: frameRect.minX, y: frameRect.minY)
        case 1: // 右上角
            return CGPoint(x: frameRect.maxX, y: frameRect.minY)
        case 2: // 左下角
            return CGPoint(x: frameRect.minX, y: frameRect.maxY)
        case 3: // 右下角
            return CGPoint(x: frameRect.maxX, y: frameRect.maxY)
        default:
            return CGPoint.zero
        }
    }

    // 计算边缘中点位置
    private func edgePosition(for index: Int) -> CGPoint {
        switch index {
        case 0: // 上边中点
            return CGPoint(x: frameRect.midX, y: frameRect.minY)
        case 1: // 右边中点
            return CGPoint(x: frameRect.maxX, y: frameRect.midY)
        case 2: // 下边中点
            return CGPoint(x: frameRect.midX, y: frameRect.maxY)
        case 3: // 左边中点
            return CGPoint(x: frameRect.minX, y: frameRect.midY)
        default:
            return CGPoint.zero
        }
    }
}
