import SwiftUI
import PhotosUI
import UIKit

// 图片选择器视图
struct ImagePickerView: UIViewControllerRepresentable {
    let sourceType: UIImagePickerController.SourceType
    let onImageSelected: (UIImage?) -> Void
    @Environment(\.presentationMode) var presentationMode

    init(sourceType: UIImagePickerController.SourceType, onImageSelected: @escaping (UIImage?) -> Void) {
        self.sourceType = sourceType
        self.onImageSelected = onImageSelected
    }

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = sourceType
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePickerView

        init(_ parent: ImagePickerView) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImageSelected(image)
            } else {
                parent.onImageSelected(nil)
            }
            parent.presentationMode.wrappedValue.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.onImageSelected(nil)
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// 背景选择弹出菜单
struct BackgroundPickerSheet: View {
    @Binding var isPresented: Bool
    var onTemplateSelected: () -> Void
    var onCameraSelected: () -> Void
    var onPhotoLibrarySelected: () -> Void

    var body: some View {
        ZStack(alignment: .bottom) {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                    .zIndex(0)
            }

            // 底部菜单
            VStack(spacing: 0) {
                // 提示文字
                Text("请选择您的背景")
                    .font(Font.custom("PingFang SC", size: 18).weight(.medium))
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    .padding(.top, 20)
                    .padding(.bottom, 20)

                VStack(spacing: 18) {
                    // 在线模版按钮
                    Button(action: {
                        onTemplateSelected()
                    }) {
                        ZStack(alignment: .center) {
                            // 背景矩形
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(maxWidth: .infinity, maxHeight: 50)
                                .background(.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .inset(by: 0.25)
                                        .stroke(
                                            Color(red: 0.98, green: 0.85, blue: 0.37),
                                            lineWidth: 0.50
                                        )
                                )
                                .shadow(
                                    color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                    radius: 10
                                )

                            // 图标和文本的组合
                            HStack(spacing: 15) {
                                Image("c-3") // 使用模版图标
                                    .frame(width: 20, height: 20)

                                Text("在线模版")
                                    .font(Font.custom("MiSans", size: 18))
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            }
                        }
                    }

                    // 拍照上传按钮
                    Button(action: {
                        onCameraSelected()
                    }) {
                        ZStack(alignment: .center) {
                            // 背景矩形
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(maxWidth: .infinity, maxHeight: 50)
                                .background(.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .inset(by: 0.25)
                                        .stroke(
                                            Color(red: 0.98, green: 0.85, blue: 0.37),
                                            lineWidth: 0.50
                                        )
                                )
                                .shadow(
                                    color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                    radius: 10
                                )

                            // 图标和文本的组合
                            HStack(spacing: 15) {
                                Image("c-1")
                                    .frame(width: 20, height: 20)

                                Text("拍照上传")
                                    .font(Font.custom("MiSans", size: 18))
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            }
                        }
                    }

                    // 本地相册按钮
                    Button(action: {
                        onPhotoLibrarySelected()
                    }) {
                        ZStack(alignment: .center) {
                            // 背景矩形
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(maxWidth: .infinity, maxHeight: 50)
                                .background(.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .inset(by: 0.25)
                                        .stroke(
                                            Color(red: 0.98, green: 0.85, blue: 0.37),
                                            lineWidth: 0.50
                                        )
                                )
                                .shadow(
                                    color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                    radius: 10
                                )

                            // 图标和文本的组合
                            HStack(spacing: 15) {
                                Image("c-2")
                                    .frame(width: 20, height: 20)

                                Text("本地相册")
                                    .font(Font.custom("MiSans", size: 18))
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            }
                        }
                    }
                }
                .padding(.bottom, 30)
                .padding(.horizontal, 20)
            }
            .background(
                Rectangle()
                    .fill(Color.white) // 改为白色背景
                    .cornerRadius(20, corners: [.topLeft, .topRight])
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: -5)
                    .frame(width: UIScreen.main.bounds.width) // 将宽度设置为屏幕宽度
            )
            .offset(y: isPresented ? 0 : UIScreen.main.bounds.height)
            .animation(.spring(), value: isPresented)
            .zIndex(1)
            .accessibilityElement(children: .contain) // 修复AX Safe category错误
        }
        .ignoresSafeArea()
    }
}

// 人物选择弹出菜单（不包含在线模版选项）
struct CharacterPickerSheet: View {
    @Binding var isPresented: Bool
    var onCameraSelected: () -> Void
    var onPhotoLibrarySelected: () -> Void

    var body: some View {
        ZStack(alignment: .bottom) {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                    .zIndex(0)
            }

            // 底部菜单
            VStack(spacing: 0) {
                // 提示文字
                Text("请选择您的人物")
                    .font(Font.custom("PingFang SC", size: 18).weight(.medium))
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    .padding(.top, 20)
                    .padding(.bottom, 20)

                VStack(spacing: 18) {
                    // 拍照上传按钮
                    Button(action: {
                        onCameraSelected()
                    }) {
                        ZStack(alignment: .center) {
                            // 背景矩形
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(maxWidth: .infinity, maxHeight: 50)
                                .background(.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .inset(by: 0.25)
                                        .stroke(
                                            Color(red: 0.98, green: 0.85, blue: 0.37),
                                            lineWidth: 0.50
                                        )
                                )
                                .shadow(
                                    color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                    radius: 10
                                )

                            // 图标和文本的组合
                            HStack(spacing: 15) {
                                Image("c-1")
                                    .frame(width: 20, height: 20)

                                Text("拍照上传")
                                    .font(Font.custom("MiSans", size: 18))
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            }
                        }
                    }

                    // 本地相册按钮
                    Button(action: {
                        onPhotoLibrarySelected()
                    }) {
                        ZStack(alignment: .center) {
                            // 背景矩形
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(maxWidth: .infinity, maxHeight: 50)
                                .background(.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .inset(by: 0.25)
                                        .stroke(
                                            Color(red: 0.98, green: 0.85, blue: 0.37),
                                            lineWidth: 0.50
                                        )
                                )
                                .shadow(
                                    color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                    radius: 10
                                )

                            // 图标和文本的组合
                            HStack(spacing: 15) {
                                Image("c-2")
                                    .frame(width: 20, height: 20)

                                Text("本地相册")
                                    .font(Font.custom("MiSans", size: 18))
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            }
                        }
                    }
                }
                .padding(.bottom, 30)
                .padding(.horizontal, 20)
            }
            .background(
                Rectangle()
                    .fill(Color.white) // 改为白色背景
                    .cornerRadius(20, corners: [.topLeft, .topRight])
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: -5)
                    .frame(width: UIScreen.main.bounds.width) // 将宽度设置为屏幕宽度
            )
            .offset(y: isPresented ? 0 : UIScreen.main.bounds.height)
            .animation(.spring(), value: isPresented)
            .zIndex(1)
            .accessibilityElement(children: .contain) // 修复AX Safe category错误
        }
        .ignoresSafeArea()
    }
}

// 保留原有的ImagePickerSheet用于向后兼容
struct ImagePickerSheet: View {
    @Binding var isPresented: Bool
    var onCameraSelected: () -> Void
    var onPhotoLibrarySelected: () -> Void

    var body: some View {
        BackgroundPickerSheet(
            isPresented: $isPresented,
            onTemplateSelected: {
                // 暂时保持空实现，后续会被替换
            },
            onCameraSelected: onCameraSelected,
            onPhotoLibrarySelected: onPhotoLibrarySelected
        )
    }
}

// 修改原有的CreateSheet，现在用于背景选择
struct CreateSheet: View {
    @Binding var isPresented: Bool
    @State private var showImagePicker = false
    @State private var showCamera = false
    @State private var showTemplateView = false
    @State private var selectedImage: UIImage?
    @State private var sourceType: UIImagePickerController.SourceType = .camera
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        BackgroundPickerSheet(
            isPresented: $isPresented,
            onTemplateSelected: {
                isPresented = false
                showTemplateView = true
            },
            onCameraSelected: {
                sourceType = .camera
                isPresented = false
                showCamera = true
            },
            onPhotoLibrarySelected: {
                sourceType = .photoLibrary
                isPresented = false
                showImagePicker = true
            }
        )
        .fullScreenCover(isPresented: $showImagePicker) {
            ImagePickerView(sourceType: .photoLibrary) { image in
                if let image = image {
                    self.selectedImage = image
                    handleSelectedImage()
                }
            }
        }
        .fullScreenCover(isPresented: $showCamera) {
            ImagePickerView(sourceType: .camera) { image in
                if let image = image {
                    self.selectedImage = image
                    handleSelectedImage()
                }
            }
        }
        .fullScreenCover(isPresented: $showTemplateView) {
            TemplateGalleryView(onSelectTemplate: { image in
                self.selectedImage = image
                handleSelectedImage()
            })
        }
    }

    // 处理选择的背景图片
    private func handleSelectedImage() {
        guard let image = selectedImage else {
            return
        }

        // 显示图像调节界面，使用主队列延迟处理，确保UI更新完成
        DispatchQueue.main.async {
            presentImageAdjustmentView(backgroundImage: image)
        }
    }

    // 直接以模态方式显示图像调节界面
    private func presentImageAdjustmentView(backgroundImage: UIImage) {
        let hostingController = UIHostingController(rootView:
            ImageAdjustmentView(
                foregroundImage: UIImage(), // 初始时没有前景图
                backgroundImage: backgroundImage,
                onBack: {
                    // 返回时关闭当前视图
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {
                        var currentVC = rootViewController
                        while let presented = currentVC.presentedViewController {
                            currentVC = presented
                        }
                        currentVC.dismiss(animated: true)
                    }
                }
            )
        )
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            hostingController.modalPresentationStyle = .fullScreen
            rootViewController.present(hostingController, animated: true) {
            }
        }
    }
}

struct create: View {
    @State private var isPresented = true
    
    var body: some View {
        CreateSheet(
            isPresented: $isPresented
        )
    }
}

struct createPreviews: PreviewProvider {
    static var previews: some View {
        Group {
            create()
        }
    }
}

